"""
Main entry point for the Cryptocurrency Pattern Screener
"""
import logging
import sys
import signal
import argparse
from dashboard import Dashboard
from config import TIMEFRAME_CONFIG, TIME_CONFIG

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('screener.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def signal_handler(sig, frame):
    """Handle shutdown signals gracefully"""
    logger.info("Received shutdown signal. Stopping screener...")
    sys.exit(0)

def parse_arguments():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(
        description='Cryptocurrency Pattern Screener - Real-time analysis of trading patterns',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Default 4-hour timeframe
  python main.py --timeframe 4h     # 4-hour candles
  python main.py --timeframe 12h    # 12-hour candles
  python main.py --timeframe 1d     # Daily candles
  python main.py -t 12h             # Short form

Timeframes:
  4h   - 4-hour candles (updates every 30 seconds)
  12h  - 12-hour candles (updates every 2 minutes)
  1d   - Daily candles (updates every 5 minutes)
        """
    )

    parser.add_argument(
        '--timeframe', '-t',
        choices=['4h', '12h', '1d'],
        default=TIME_CONFIG['default_timeframe'],
        help='Timeframe for pattern analysis (default: %(default)s)'
    )

    parser.add_argument(
        '--port', '-p',
        type=int,
        default=5000,
        help='Port for web dashboard (default: %(default)s)'
    )

    return parser.parse_args()

def main():
    """Main function to start the cryptocurrency screener"""
    # Parse command-line arguments
    args = parse_arguments()

    # Get timeframe configuration
    timeframe_config = TIMEFRAME_CONFIG[args.timeframe]

    logger.info(f"Starting Cryptocurrency {timeframe_config['display_name']} Pattern Screener")
    logger.info(f"Timeframe: {timeframe_config['display_name']} ({args.timeframe})")
    logger.info(f"Update interval: {timeframe_config['update_interval']} seconds")

    # Setup signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Create and start dashboard with timeframe configuration
        dashboard = Dashboard(
            port=args.port,
            timeframe=args.timeframe,
            timeframe_config=timeframe_config
        )

        logger.info(f"Dashboard will be available at: http://localhost:{args.port}")
        logger.info("Press Ctrl+C to stop the screener")

        # Start the dashboard (this will block)
        dashboard.start()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt. Shutting down...")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        logger.info("Screener stopped")

if __name__ == "__main__":
    main()
