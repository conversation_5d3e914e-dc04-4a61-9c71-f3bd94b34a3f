"""
Bybit API client for fetching cryptocurrency market data
"""
import requests
import pandas as pd
from datetime import datetime, timezone
from typing import List, Dict, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BybitAPI:
    def __init__(self, timeframe_config=None):
        self.base_url = "https://api.bybit.com"
        self.session = requests.Session()
        self.timeframe_config = timeframe_config or {
            'bybit_interval': '240',
            'interval_minutes': 240,
            'display_name': '4-Hour'
        }

    def get_perpetual_pairs(self) -> List[str]:
        """Get all USDT perpetual trading pairs from Bybit"""
        try:
            url = f"{self.base_url}/v5/market/instruments-info"
            params = {
                'category': 'linear',
                'status': 'Trading'
            }

            response = self.session.get(url, params=params)
            response.raise_for_status()

            data = response.json()
            pairs = []

            for instrument in data['result']['list']:
                symbol = instrument['symbol']
                if symbol.endswith('USDT') and instrument['status'] == 'Trading':
                    pairs.append(symbol)

            logger.info(f"Found {len(pairs)} USDT perpetual pairs")
            return sorted(pairs)

        except Exception as e:
            logger.error(f"Error fetching perpetual pairs: {e}")
            return []

    def get_kline_data(self, symbol: str, interval: str = None, limit: int = 200) -> pd.DataFrame:
        """
        Get historical kline data for a symbol
        interval: Uses timeframe_config if not specified
        """
        if interval is None:
            interval = self.timeframe_config['bybit_interval']
        try:
            url = f"{self.base_url}/v5/market/kline"
            params = {
                'category': 'linear',
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }

            response = self.session.get(url, params=params)
            response.raise_for_status()

            data = response.json()
            klines = data['result']['list']

            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
            ])

            # Convert data types
            df['timestamp'] = pd.to_datetime(df['timestamp'].astype(int), unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)

            # Sort by timestamp (oldest first)
            df = df.sort_values('timestamp').reset_index(drop=True)

            return df

        except Exception as e:
            logger.error(f"Error fetching kline data for {symbol}: {e}")
            return pd.DataFrame()

    def get_current_price_and_volume(self, symbol: str) -> Dict:
        """Get current price and 24h volume data for a symbol"""
        try:
            url = f"{self.base_url}/v5/market/tickers"
            params = {
                'category': 'linear',
                'symbol': symbol
            }

            response = self.session.get(url, params=params)
            response.raise_for_status()

            data = response.json()
            if data['result']['list']:
                ticker = data['result']['list'][0]
                return {
                    'price': float(ticker['lastPrice']),
                    'volume_24h': float(ticker['volume24h']),
                    'turnover_24h': float(ticker['turnover24h'])
                }

            return {}

        except Exception as e:
            logger.error(f"Error fetching ticker data for {symbol}: {e}")
            return {}

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol (backward compatibility)"""
        ticker_data = self.get_current_price_and_volume(symbol)
        return ticker_data.get('price')

    def get_current_candle_info(self, symbol: str) -> Dict:
        """Get information about the current developing candle with volume data"""
        try:
            # Get the latest kline data (limit=2 to get current and previous)
            df = self.get_kline_data(symbol, limit=2)

            if df.empty:
                return {}

            current_candle = df.iloc[-1]

            # Get current price and volume data
            ticker_data = self.get_current_price_and_volume(symbol)
            current_price = ticker_data.get('price', current_candle['close'])

            # Calculate current candle metrics with live price
            open_price = current_candle['open']
            high_price = max(current_candle['high'], current_price)
            low_price = min(current_candle['low'], current_price)

            return {
                'symbol': symbol,
                'timestamp': current_candle['timestamp'],
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': current_price,
                'volume_24h': ticker_data.get('volume_24h', 0),
                'turnover_24h': ticker_data.get('turnover_24h', 0),
                'is_developing': True
            }

        except Exception as e:
            logger.error(f"Error getting current candle info for {symbol}: {e}")
            return {}

    def is_new_candle(self) -> bool:
        """Check if we're at the start of a new candle (UTC aligned)"""
        now = datetime.now(timezone.utc)
        interval_hours = self.timeframe_config['interval_minutes'] // 60

        if interval_hours == 4:
            # 4-hour candles start at 00:00, 04:00, 08:00, 12:00, 16:00, 20:00 UTC
            return now.hour % 4 == 0 and now.minute < 2
        elif interval_hours == 12:
            # 12-hour candles start at 00:00, 12:00 UTC
            return now.hour % 12 == 0 and now.minute < 5
        elif interval_hours == 24:
            # Daily candles start at 00:00 UTC
            return now.hour == 0 and now.minute < 10
        else:
            # Default fallback
            return False

    def is_new_4h_candle(self) -> bool:
        """Backward compatibility method"""
        return self.is_new_candle()
