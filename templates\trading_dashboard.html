<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Paper Trading Bot - {{ timeframe_config.display_name }} Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
            color: #f8fafc;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #262626 0%, #1a1a1a 100%);
            border-radius: 20px;
            border: 1px solid #374151;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: linear-gradient(135deg, #262626 0%, #1f1f1f 100%);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid #374151;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border-color: #4b5563;
        }

        .card h3 {
            margin-bottom: 20px;
            font-size: 1.4em;
            border-bottom: 2px solid #374151;
            padding-bottom: 12px;
            color: #f8fafc;
            font-weight: 600;
        }

        .portfolio-summary {
            grid-column: 1 / -1;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .summary-item {
            text-align: center;
            padding: 18px;
            background: linear-gradient(135deg, #1f1f1f 0%, #171717 100%);
            border-radius: 12px;
            border: 1px solid #2d2d2d;
            transition: all 0.3s ease;
        }

        .summary-item:hover {
            transform: translateY(-1px);
            border-color: #3b82f6;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
        }

        .summary-item .label {
            font-size: 0.85em;
            color: #9ca3af;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .summary-item .value {
            font-size: 1.5em;
            font-weight: 700;
            color: #f8fafc;
        }

        .positive { color: #10b981; }
        .negative { color: #ef4444; }
        .neutral { color: #f8fafc; }

        .table-container {
            overflow-x: auto;
            margin-top: 15px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: #171717;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #2d2d2d;
        }

        th, td {
            padding: 14px 16px;
            text-align: left;
            border-bottom: 1px solid #2d2d2d;
        }

        th {
            background: linear-gradient(135deg, #262626 0%, #1f1f1f 100%);
            font-weight: 600;
            color: #f8fafc;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:hover {
            background: #1f1f1f;
        }

        tbody tr {
            transition: background-color 0.2s ease;
        }

        .btn {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
            border-color: #f87171;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            margin-bottom: 24px;
            padding: 14px 28px;
            font-size: 1em;
            font-weight: 600;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
        }

        .refresh-btn:hover {
            box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
            border-color: #60a5fa;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
            box-shadow: 0 0 8px currentColor;
        }

        .status-active {
            background-color: #10b981;
            color: #10b981;
        }
        .status-inactive {
            background-color: #ef4444;
            color: #ef4444;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #9ca3af;
        }

        .error {
            background: linear-gradient(135deg, #1f1f1f 0%, #171717 100%);
            border: 1px solid #ef4444;
            color: #fecaca;
            padding: 18px;
            border-radius: 12px;
            margin: 24px 0;
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.1);
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #6b7280;
            font-style: italic;
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Crypto Paper Trading Bot</h1>
            <div class="subtitle">{{ timeframe_config.display_name }} Timeframe • Pinbar Pattern Strategy</div>
        </div>

        <button class="btn refresh-btn" onclick="refreshPortfolio()">🔄 Refresh Portfolio</button>

        <div id="loading" class="loading">
            <div>Loading portfolio data...</div>
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="dashboard" style="display: none;">
            <!-- Portfolio Summary -->
            <div class="card portfolio-summary">
                <h3>📊 Portfolio Summary</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="label">Current Balance</div>
                        <div class="value neutral" id="current-balance">$0.00</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">Total Value</div>
                        <div class="value neutral" id="total-value">$0.00</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">Total P&L</div>
                        <div class="value" id="total-pnl">$0.00</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">Daily P&L</div>
                        <div class="value" id="daily-pnl">$0.00</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">Unrealized P&L</div>
                        <div class="value" id="unrealized-pnl">$0.00</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">Win Rate</div>
                        <div class="value neutral" id="win-rate">0%</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">Open Positions</div>
                        <div class="value neutral" id="open-positions">0</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">Total Trades</div>
                        <div class="value neutral" id="total-trades">0</div>
                    </div>
                </div>
            </div>

            <div class="dashboard-grid">
                <!-- Open Positions -->
                <div class="card">
                    <h3>📈 Open Positions</h3>
                    <div class="table-container">
                        <table id="positions-table">
                            <thead>
                                <tr>
                                    <th>Symbol</th>
                                    <th>Type</th>
                                    <th>Entry</th>
                                    <th>Current</th>
                                    <th>P&L</th>
                                    <th>Pattern</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody id="positions-body">
                            </tbody>
                        </table>
                        <div id="no-positions" class="no-data" style="display: none;">
                            No open positions
                        </div>
                    </div>
                </div>

                <!-- Recent Trades -->
                <div class="card">
                    <h3>📋 Recent Trades</h3>
                    <div class="table-container">
                        <table id="trades-table">
                            <thead>
                                <tr>
                                    <th>Symbol</th>
                                    <th>Type</th>
                                    <th>P&L</th>
                                    <th>R:R</th>
                                    <th>Exit Reason</th>
                                </tr>
                            </thead>
                            <tbody id="trades-body">
                            </tbody>
                        </table>
                        <div id="no-trades" class="no-data" style="display: none;">
                            No completed trades yet
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Signals -->
            <div class="card">
                <h3>🎯 Active Trading Signals</h3>
                <div class="table-container">
                    <table id="signals-table">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Type</th>
                                <th>Entry Price</th>
                                <th>Stop Loss</th>
                                <th>Confidence</th>
                                <th>Pattern</th>
                                <th>Generated</th>
                            </tr>
                        </thead>
                        <tbody id="signals-body">
                        </tbody>
                    </table>
                    <div id="no-signals" class="no-data" style="display: none;">
                        No active trading signals
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let portfolioData = null;
        let updateInterval = {{ timeframe_config.update_interval * 1000 }};

        function formatCurrency(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value);
        }

        function formatPrice(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 4,
                maximumFractionDigits: 4
            }).format(value);
        }

        function formatPercent(value) {
            return value.toFixed(1) + '%';
        }

        function getColorClass(value) {
            if (value > 0) return 'positive';
            if (value < 0) return 'negative';
            return 'neutral';
        }

        function updatePortfolioSummary(summary) {
            document.getElementById('current-balance').textContent = formatCurrency(summary.current_balance);
            document.getElementById('total-value').textContent = formatCurrency(summary.total_value);
            
            const totalPnlEl = document.getElementById('total-pnl');
            totalPnlEl.textContent = formatCurrency(summary.total_pnl);
            totalPnlEl.className = 'value ' + getColorClass(summary.total_pnl);
            
            const dailyPnlEl = document.getElementById('daily-pnl');
            dailyPnlEl.textContent = formatCurrency(summary.daily_pnl);
            dailyPnlEl.className = 'value ' + getColorClass(summary.daily_pnl);
            
            const unrealizedPnlEl = document.getElementById('unrealized-pnl');
            unrealizedPnlEl.textContent = formatCurrency(summary.unrealized_pnl);
            unrealizedPnlEl.className = 'value ' + getColorClass(summary.unrealized_pnl);
            
            document.getElementById('win-rate').textContent = formatPercent(summary.win_rate);
            document.getElementById('open-positions').textContent = summary.open_positions;
            document.getElementById('total-trades').textContent = summary.total_trades;
        }

        function updatePositionsTable(positions) {
            const tbody = document.getElementById('positions-body');
            const noPositions = document.getElementById('no-positions');
            
            if (positions.length === 0) {
                tbody.innerHTML = '';
                noPositions.style.display = 'block';
                return;
            }
            
            noPositions.style.display = 'none';
            tbody.innerHTML = positions.map(pos => `
                <tr>
                    <td>${pos.symbol}</td>
                    <td><span class="status-indicator status-active"></span>${pos.type.toUpperCase()}</td>
                    <td>${formatPrice(pos.entry_price)}</td>
                    <td>${formatPrice(pos.current_price)}</td>
                    <td class="${getColorClass(pos.unrealized_pnl)}">${formatCurrency(pos.unrealized_pnl)}</td>
                    <td>${pos.pattern_type.replace(/_/g, ' ')}</td>
                    <td><button class="btn" onclick="closePosition('${pos.id}')">Close</button></td>
                </tr>
            `).join('');
        }

        function updateTradesTable(trades) {
            const tbody = document.getElementById('trades-body');
            const noTrades = document.getElementById('no-trades');
            
            if (trades.length === 0) {
                tbody.innerHTML = '';
                noTrades.style.display = 'block';
                return;
            }
            
            noTrades.style.display = 'none';
            tbody.innerHTML = trades.map(trade => `
                <tr>
                    <td>${trade.symbol}</td>
                    <td>${trade.type.toUpperCase()}</td>
                    <td class="${getColorClass(trade.realized_pnl)}">${formatCurrency(trade.realized_pnl)}</td>
                    <td>${trade.risk_reward_ratio.toFixed(2)}</td>
                    <td>${trade.exit_reason}</td>
                </tr>
            `).join('');
        }

        function updateSignalsTable(signals) {
            const tbody = document.getElementById('signals-body');
            const noSignals = document.getElementById('no-signals');
            
            if (signals.length === 0) {
                tbody.innerHTML = '';
                noSignals.style.display = 'block';
                return;
            }
            
            noSignals.style.display = 'none';
            tbody.innerHTML = signals.map(signal => `
                <tr>
                    <td>${signal.symbol}</td>
                    <td>${signal.type.toUpperCase()}</td>
                    <td>${formatPrice(signal.entry_price)}</td>
                    <td>${formatPrice(signal.stop_loss)}</td>
                    <td>${signal.confidence.toFixed(1)}%</td>
                    <td>${signal.pattern_type.replace(/_/g, ' ')}</td>
                    <td>${new Date(signal.generated_at).toLocaleTimeString()}</td>
                </tr>
            `).join('');
        }

        function updateDashboard(data) {
            portfolioData = data;
            
            if (data.error) {
                showError(data.error);
                return;
            }
            
            hideError();
            
            updatePortfolioSummary(data.portfolio_summary);
            updatePositionsTable(data.positions || []);
            updateTradesTable(data.recent_trades || []);
            updateSignalsTable(data.active_signals || []);
            
            document.getElementById('loading').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
        }

        function showError(message) {
            const errorEl = document.getElementById('error');
            errorEl.textContent = 'Error: ' + message;
            errorEl.style.display = 'block';
            document.getElementById('loading').style.display = 'none';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        function loadPortfolioData() {
            fetch('/api/portfolio')
                .then(response => response.json())
                .then(data => updateDashboard(data))
                .catch(error => showError('Failed to load portfolio data: ' + error.message));
        }

        function refreshPortfolio() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('dashboard').style.display = 'none';
            
            fetch('/api/refresh')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showError(data.error);
                    } else {
                        setTimeout(loadPortfolioData, 1000); // Wait a bit for data to update
                    }
                })
                .catch(error => showError('Failed to refresh portfolio: ' + error.message));
        }

        function closePosition(positionId) {
            if (!confirm('Are you sure you want to close this position?')) {
                return;
            }
            
            fetch(`/api/close_position/${positionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('Error closing position: ' + data.error);
                    } else {
                        alert('Position closed successfully');
                        loadPortfolioData(); // Refresh data
                    }
                })
                .catch(error => alert('Failed to close position: ' + error.message));
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadPortfolioData();
            
            // Auto-refresh every update interval
            setInterval(loadPortfolioData, updateInterval);
        });
    </script>
</body>
</html>
