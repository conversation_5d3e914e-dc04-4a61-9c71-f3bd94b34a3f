"""
Configuration settings for the Cryptocurrency 4H Pattern Screener
"""

# Pattern Detection Settings
PATTERN_CONFIG = {
    # Pinbar detection criteria
    'pinbar_min_wick_ratio': 2.0,          # Minimum wick-to-body ratio for pinbars
    'pinbar_body_position_threshold': 0.33, # Body must be in upper/lower 1/3 of range

    # Long wick detection criteria
    'long_wick_min_ratio': 1.5,            # Minimum wick-to-body ratio for long wicks

    # Filtering criteria
    'max_ema_distance': 5.0,               # Maximum % distance from 55 EMA to consider
    'min_pattern_strength': 1.5,           # Minimum pattern strength to display
}

# API Settings
API_CONFIG = {
    'max_workers': 10,                      # Number of concurrent API requests
    'request_timeout': 30,                  # Timeout for individual API requests (seconds)
    'historical_candles': 100,              # Number of historical candles to fetch for EMA
}

# Dashboard Settings
DASHBOARD_CONFIG = {
    'port': 5000,                          # Web dashboard port
    'update_interval': 30,                 # Current patterns update interval (seconds)
    'candle_check_interval': 60,           # Candle transition check interval (seconds)
}

# Logging Settings
LOGGING_CONFIG = {
    'level': 'INFO',                       # Logging level (DEBUG, INFO, WARNING, ERROR)
    'log_file': 'screener.log',           # Log file name
    'max_log_size': 10 * 1024 * 1024,    # Maximum log file size (10MB)
    'backup_count': 5,                     # Number of backup log files to keep
}

# Timeframe Settings
TIMEFRAME_CONFIG = {
    '4h': {
        'interval_minutes': 240,           # 4 hours in minutes
        'bybit_interval': '240',           # Bybit API interval parameter
        'update_interval': 30,             # Update every 30 seconds
        'candle_check_interval': 60,       # Check for new candles every minute
        'display_name': '4-Hour',
        'short_name': '4H'
    },
    '12h': {
        'interval_minutes': 720,           # 12 hours in minutes
        'bybit_interval': '720',           # Bybit API interval parameter
        'update_interval': 120,            # Update every 2 minutes
        'candle_check_interval': 300,      # Check for new candles every 5 minutes
        'display_name': '12-Hour',
        'short_name': '12H'
    },
    '1d': {
        'interval_minutes': 1440,          # 24 hours in minutes
        'bybit_interval': 'D',             # Bybit API interval parameter (D for daily)
        'update_interval': 300,            # Update every 5 minutes
        'candle_check_interval': 600,      # Check for new candles every 10 minutes
        'display_name': 'Daily',
        'short_name': '1D'
    }
}

# Time Settings
TIME_CONFIG = {
    'timezone': 'UTC',                     # Timezone for candle alignment
    'default_timeframe': '4h',             # Default timeframe if none specified
}

# Display Settings
DISPLAY_CONFIG = {
    'max_patterns_per_table': 50,          # Maximum patterns to show in each table
    'price_decimal_places': 4,             # Decimal places for price display
    'percentage_decimal_places': 1,        # Decimal places for percentage display
}
