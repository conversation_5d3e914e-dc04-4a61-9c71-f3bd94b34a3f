# Cryptocurrency Pattern Screener

A real-time cryptocurrency trading opportunity screener that analyzes Bybit perpetual trading pairs on multiple timeframes to detect pinbar candlestick patterns and long wicks.

## Features

- **Multiple Timeframes**: Support for 4-hour, 12-hour, and daily candle analysis
- **Real-time Analysis**: Monitors all Bybit USDT perpetual pairs on selected timeframes
- **Pattern Detection**: Identifies pinbars and long wick patterns as they develop
- **EMA Integration**: Calculates distance from 55-period Exponential Moving Average
- **Volume-Weighted Strength**: 24-hour turnover boosts pattern strength (up to 2x multiplier)
- **Sortable Columns**: Click column headers to sort by any metric
- **Live Dashboard**: Web-based interface with timeframe-adaptive auto-refresh
- **Clickable Trading Links**: Direct links to Bybit trading pages
- **Volume Highlighting**: Visual indicators for high/medium/low volume pairs
- **Command-line Interface**: Easy timeframe selection via command-line arguments

## Pattern Detection Criteria

### Pinbars
- Wick length ≥ 2x body size
- Body positioned in upper/lower 1/3 of candle range
- **<PERSON><PERSON> Pinbar**: Long lower wick, body in upper third
- **Bearish Pinbar**: Long upper wick, body in lower third

### Long Wicks
- Single wick ≥ 1.5x body size
- Can be upper or lower wicks

### Volume-Weighted Strength
- **Base Strength**: Raw pattern strength from wick-to-body ratio
- **Volume Multiplier**: Based on 24-hour USDT turnover
  - 100M+ USDT: 2.0x multiplier
  - 50M+ USDT: 1.8x multiplier
  - 20M+ USDT: 1.6x multiplier
  - 10M+ USDT: 1.4x multiplier
  - 5M+ USDT: 1.2x multiplier
  - 1M+ USDT: 1.1x multiplier
- **Final Strength**: Base Strength × Volume Multiplier

### Filtering
- Only shows patterns within ±5% of 55 EMA
- Minimum pattern strength of 1.5x (after volume weighting)

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Screener**:
   ```bash
   # Default 4-hour timeframe
   python main.py

   # Specific timeframes
   python main.py --timeframe 4h     # 4-hour candles
   python main.py --timeframe 12h    # 12-hour candles
   python main.py --timeframe 1d     # Daily candles

   # Short form
   python main.py -t 12h

   # Custom port
   python main.py --timeframe 1d --port 8080
   ```
   Or on Windows, double-click: `start_screener.bat` (uses default 4h)

3. **Access Dashboard**:
   Open your browser to `http://localhost:5000` (or custom port)

4. **Check Status**:
   ```bash
   python check_status.py
   ```

5. **Test System**:
   ```bash
   python test_screener.py
   ```

## Timeframe Options

| Timeframe | Command | Update Frequency | Candle Check | Best For |
|-----------|---------|------------------|--------------|----------|
| **4-Hour** | `python main.py -t 4h` | 30 seconds | 1 minute | Intraday trading |
| **12-Hour** | `python main.py -t 12h` | 2 minutes | 5 minutes | Swing trading |
| **Daily** | `python main.py -t 1d` | 5 minutes | 10 minutes | Position trading |

## Dashboard Layout

### Current Candle Table
- Shows developing patterns in the current 4-hour candle
- Updates every 30 seconds
- Patterns may change as the candle develops
- **Sortable columns**: Click headers to sort by Pair, Pattern, Strength, Volume, or EMA Distance

### Previous Candle Table
- Shows confirmed patterns from the last completed 4-hour candle
- Updated when new candles close (every 4 hours)
- More reliable for trading decisions
- **Sortable columns**: Independent sorting from current table

### Column Details
- **Pair**: Trading pair symbol (clickable for Bybit trading)
- **Pattern**: Type of pattern detected (color-coded)
- **Strength**: Final strength showing base × volume multiplier
- **24h Volume**: USDT turnover with visual highlighting
- **EMA Distance**: Percentage distance from 55 EMA
- **Action**: Direct link to Bybit trading page

## 4-Hour Candle Schedule (UTC)
- 00:00 - 04:00
- 04:00 - 08:00
- 08:00 - 12:00
- 12:00 - 16:00
- 16:00 - 20:00
- 20:00 - 00:00

## Local Time Context
If your local time is 6 hours ahead of UTC:
- 4hr candle starts at 6pm local
- 4hr candle closes at 10pm local

## File Structure

```
├── main.py                 # Application entry point
├── bybit_api.py           # Bybit API client
├── technical_analysis.py  # Pattern detection algorithms
├── screener.py            # Main screening engine
├── dashboard.py           # Web dashboard
├── config.py              # Configuration settings
├── test_screener.py       # Test suite
├── check_status.py        # Status checker
├── start_screener.bat     # Windows startup script (4h default)
├── start_screener_12h.bat # Windows startup script (12h)
├── start_screener_1d.bat  # Windows startup script (1d)
├── templates/
│   └── dashboard.html     # Dashboard HTML template
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## Configuration

You can modify settings in `config.py`:

**Pattern Detection:**
- `pinbar_min_wick_ratio`: Minimum wick-to-body ratio for pinbars (default: 2.0x)
- `pinbar_body_position_threshold`: Body position threshold (default: 0.33)
- `long_wick_min_ratio`: Minimum wick-to-body ratio for long wicks (default: 1.5x)
- `max_ema_distance`: Maximum % distance from EMA (default: 5.0%)
- `min_pattern_strength`: Minimum pattern strength (default: 1.5x)

**Performance:**
- `max_workers`: Concurrent API requests (default: 10)
- `update_interval`: Current patterns update frequency (default: 30s)

## Logging

The application logs to both console and `screener.log` file. Check the log file for detailed information about API calls, pattern detection, and any errors.

## Troubleshooting

1. **No patterns detected**: This is normal - patterns are relatively rare
2. **API errors**: Check your internet connection and Bybit API status
3. **Dashboard not loading**: Ensure port 5000 is available

## Advanced Usage

### Command Line Examples
```bash
# Help and options
python main.py --help

# Different timeframes
python main.py --timeframe 4h --port 5000
python main.py --timeframe 12h --port 8080
python main.py --timeframe 1d --port 9000

# Run multiple instances (different ports)
python main.py -t 4h -p 5000 &   # 4h on port 5000
python main.py -t 12h -p 5001 &  # 12h on port 5001
python main.py -t 1d -p 5002 &   # 1d on port 5002
```

### Timeframe Characteristics
- **4-Hour**: Best for intraday trading, frequent updates, more patterns
- **12-Hour**: Good for swing trading, balanced update frequency
- **Daily**: Ideal for position trading, fewer but stronger patterns

## Disclaimer

This tool is for educational and informational purposes only. Always do your own research before making trading decisions. Cryptocurrency trading involves significant risk.

## Support

For issues or questions, check the log files for error messages and ensure all dependencies are properly installed.
